import com.tanhua.sso.controller.SmsController;
import com.tanhua.sso.service.SmsService;
import com.tanhua.sso.vo.ErrorResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class SmsControllerTest {

    @Mock
    private SmsService smsService;

    @InjectMocks
    private SmsController smsController;

    private Map<String, Object> requestParams;

    @BeforeEach
    void setUp() {
        requestParams = new HashMap<>();
        requestParams.put("phone", "18852780589");
    }

    @Test
    void testSendCheckCode_Success() {
        // Given
        Map<String, Object> serviceResult = new HashMap<>();
        serviceResult.put("code", 3);
        serviceResult.put("msg", "ok");

        when(smsService.sendCheckCode(anyString())).thenReturn(serviceResult);

        // When
        ResponseEntity<Object> response = smsController.sendCheckCode(requestParams);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNull(response.getBody());
    }

    @Test
    void testSendCheckCode_CodeAlreadyExists() {
        // Given
        Map<String, Object> serviceResult = new HashMap<>();
        serviceResult.put("code", 1);
        serviceResult.put("msg", "上一次验证码未失效");

        when(smsService.sendCheckCode(anyString())).thenReturn(serviceResult);

        // When
        ResponseEntity<Object> response = smsController.sendCheckCode(requestParams);

        // Then
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody() instanceof ErrorResult);

        ErrorResult errorResult = (ErrorResult) response.getBody();
        assertEquals("000001", errorResult.getErrCode());
        assertEquals("上一次验证码未失效", errorResult.getErrMessage());
    }

    @Test
    void testSendCheckCode_SendFailed() {
        // Given
        Map<String, Object> serviceResult = new HashMap<>();
        serviceResult.put("code", 2);
        serviceResult.put("msg", "发送验证码失败");

        when(smsService.sendCheckCode(anyString())).thenReturn(serviceResult);

        // When
        ResponseEntity<Object> response = smsController.sendCheckCode(requestParams);

        // Then
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody() instanceof ErrorResult);

        ErrorResult errorResult = (ErrorResult) response.getBody();
        assertEquals("000000", errorResult.getErrCode());
        assertEquals("短信发送失败", errorResult.getErrMessage());
    }

    @Test
    void testSendCheckCode_Exception() {
        // Given
        Map<String, Object> serviceResult = new HashMap<>();
        serviceResult.put("code", 4);
        serviceResult.put("msg", "发送验证码异常");

        when(smsService.sendCheckCode(anyString())).thenReturn(serviceResult);

        // When
        ResponseEntity<Object> response = smsController.sendCheckCode(requestParams);

        // Then
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody() instanceof ErrorResult);

        ErrorResult errorResult = (ErrorResult) response.getBody();
        assertEquals("000000", errorResult.getErrCode());
        assertEquals("短信发送失败", errorResult.getErrMessage());
    }

    @Test
    void testSendCheckCode_NullPhone() {
        // Given
        requestParams.put("phone", null);
        Map<String, Object> serviceResult = new HashMap<>();
        serviceResult.put("code", 3);
        serviceResult.put("msg", "ok");

        when(smsService.sendCheckCode("null")).thenReturn(serviceResult);

        // When
        ResponseEntity<Object> response = smsController.sendCheckCode(requestParams);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNull(response.getBody());
    }

    @Test
    void testSendCheckCode_EmptyParams() {
        // Given
        Map<String, Object> emptyParams = new HashMap<>();
        Map<String, Object> serviceResult = new HashMap<>();
        serviceResult.put("code", 3);
        serviceResult.put("msg", "ok");

        when(smsService.sendCheckCode("null")).thenReturn(serviceResult);

        // When
        ResponseEntity<Object> response = smsController.sendCheckCode(emptyParams);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNull(response.getBody());
    }
}