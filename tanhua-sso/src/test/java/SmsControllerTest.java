import com.tanhua.sso.controller.SmsController;
import com.tanhua.sso.service.SmsService;
import com.tanhua.sso.vo.ErrorResult;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class SmsControllerTest {
    SmsService smsService = new SmsService();

    SmsController smsController = new SmsController();

    @Test
    void testSuccess() {
        String phone = "18852780589";
        Map<String, Object> map = new HashMap<>();
        map.put("phone", phone);
        ResponseEntity<Object> response = smsController.sendCheckCode(map);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNull(response.getBody());
    }
}