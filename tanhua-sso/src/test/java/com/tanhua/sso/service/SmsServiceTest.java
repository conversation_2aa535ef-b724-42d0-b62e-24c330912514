package com.tanhua.sso.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SmsServiceTest {

    @Mock
    private RestTemplate restTemplate;

    @Mock
    private RedisTemplate<String, String> redisTemplate;

    @Mock
    private ValueOperations<String, String> valueOperations;

    @InjectMocks
    private SmsService smsService;

    private final String testMobile = "18852780589";
    private final String redisKey = "CHECK_CODE_" + testMobile;

    @BeforeEach
    void setUp() {
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
    }

    @Test
    void testSendCheckCode_Success() {
        // Given
        when(valueOperations.get(redisKey)).thenReturn(null);

        // When
        Map<String, Object> result = smsService.sendCheckCode(testMobile);

        // Then
        assertEquals(3, result.get("code"));
        assertEquals("ok", result.get("msg"));
        verify(valueOperations).set(eq(redisKey), anyString(), eq(Duration.ofMinutes(2)));
    }

    @Test
    void testSendCheckCode_CodeAlreadyExists() {
        // Given
        when(valueOperations.get(redisKey)).thenReturn("123456");

        // When
        Map<String, Object> result = smsService.sendCheckCode(testMobile);

        // Then
        assertEquals(1, result.get("code"));
        assertEquals("上一次验证码未失效", result.get("msg"));
        verify(valueOperations, never()).set(anyString(), anyString(), any(Duration.class));
    }

    @Test
    void testSendCheckCode_RedisException() {
        // Given
        when(valueOperations.get(redisKey)).thenThrow(new RuntimeException("Redis connection failed"));

        // When
        Map<String, Object> result = smsService.sendCheckCode(testMobile);

        // Then
        assertEquals(4, result.get("code"));
        assertEquals("发送验证码异常", result.get("msg"));
    }

    @Test
    void testSendCheckCode_EmptyMobile() {
        // Given
        String emptyMobile = "";
        String emptyRedisKey = "CHECK_CODE_" + emptyMobile;
        when(valueOperations.get(emptyRedisKey)).thenReturn(null);

        // When
        Map<String, Object> result = smsService.sendCheckCode(emptyMobile);

        // Then
        assertEquals(3, result.get("code"));
        assertEquals("ok", result.get("msg"));
    }

    @Test
    void testSendCheckCode_NullMobile() {
        // Given
        String nullMobile = null;
        String nullRedisKey = "CHECK_CODE_" + nullMobile;
        when(valueOperations.get(nullRedisKey)).thenReturn(null);

        // When
        Map<String, Object> result = smsService.sendCheckCode(nullMobile);

        // Then
        assertEquals(3, result.get("code"));
        assertEquals("ok", result.get("msg"));
    }

    @Test
    void testSendSms_Success() {
        // When
        String result = smsService.sendSms(testMobile);

        // Then
        assertNotNull(result);
        assertTrue(result.matches("\\d{6}"));
        int code = Integer.parseInt(result);
        assertTrue(code >= 100000 && code <= 999999);
    }

    @Test
    void testSendSms_EmptyMobile() {
        // When
        String result = smsService.sendSms("");

        // Then
        assertNotNull(result);
        assertTrue(result.matches("\\d{6}"));
    }

    @Test
    void testSendSms_NullMobile() {
        // When
        String result = smsService.sendSms(null);

        // Then
        assertNotNull(result);
        assertTrue(result.matches("\\d{6}"));
    }

    @Test
    void testSendCheckCode_RedisSetException() {
        // Given
        when(valueOperations.get(redisKey)).thenReturn(null);
        doThrow(new RuntimeException("Redis set failed"))
            .when(valueOperations).set(anyString(), anyString(), any(Duration.class));

        // When
        Map<String, Object> result = smsService.sendCheckCode(testMobile);

        // Then
        assertEquals(4, result.get("code"));
        assertEquals("发送验证码异常", result.get("msg"));
    }

    @Test
    void testSendCheckCode_MultipleCallsWithinTwoMinutes() {
        // Given
        when(valueOperations.get(redisKey)).thenReturn("123456");

        // When - First call
        Map<String, Object> result1 = smsService.sendCheckCode(testMobile);
        
        // When - Second call immediately
        Map<String, Object> result2 = smsService.sendCheckCode(testMobile);

        // Then
        assertEquals(1, result1.get("code"));
        assertEquals("上一次验证码未失效", result1.get("msg"));
        assertEquals(1, result2.get("code"));
        assertEquals("上一次验证码未失效", result2.get("msg"));
        
        verify(valueOperations, never()).set(anyString(), anyString(), any(Duration.class));
    }

    @Test
    void testSendCheckCode_VerifyRedisKeyFormat() {
        // Given
        String customMobile = "13912345678";
        String expectedRedisKey = "CHECK_CODE_" + customMobile;
        when(valueOperations.get(expectedRedisKey)).thenReturn(null);

        // When
        smsService.sendCheckCode(customMobile);

        // Then
        verify(valueOperations).get(expectedRedisKey);
        verify(valueOperations).set(eq(expectedRedisKey), anyString(), eq(Duration.ofMinutes(2)));
    }

    @Test
    void testSendCheckCode_VerifyCodeGeneration() {
        // Given
        when(valueOperations.get(redisKey)).thenReturn(null);

        // When
        Map<String, Object> result = smsService.sendCheckCode(testMobile);

        // Then
        assertEquals(3, result.get("code"));
        assertEquals("ok", result.get("msg"));
        
        // Verify that a 6-digit code was generated and stored
        verify(valueOperations).set(eq(redisKey), argThat(code -> 
            code != null && code.matches("\\d{6}")
        ), eq(Duration.ofMinutes(2)));
    }
}
