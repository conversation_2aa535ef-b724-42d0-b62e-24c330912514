package com.tanhua.sso.pojo;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class UserTest {

    private User user;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        user = new User();
        objectMapper = new ObjectMapper();
    }

    @Test
    void testDefaultConstructor() {
        // When
        User newUser = new User();

        // Then
        assertEquals(0, newUser.getId());
        assertNull(newUser.getMobile());
        assertNull(newUser.getPassword());
    }

    @Test
    void testAllArgsConstructor() {
        // When
        User newUser = new User(1, "18852780589", "password123");

        // Then
        assertEquals(1, newUser.getId());
        assertEquals("18852780589", newUser.getMobile());
        assertEquals("password123", newUser.getPassword());
    }

    @Test
    void testSettersAndGetters() {
        // When
        user.setId(100);
        user.setMobile("13912345678");
        user.setPassword("mypassword");

        // Then
        assertEquals(100, user.getId());
        assertEquals("13912345678", user.getMobile());
        assertEquals("mypassword", user.getPassword());
    }

    @Test
    void testEqualsAndHashCode() {
        // Given
        User user1 = new User(1, "18852780589", "password123");
        User user2 = new User(1, "18852780589", "password123");
        User user3 = new User(2, "13912345678", "password456");

        // Then
        assertEquals(user1, user2);
        assertEquals(user1.hashCode(), user2.hashCode());
        assertNotEquals(user1, user3);
        assertNotEquals(user1.hashCode(), user3.hashCode());
    }

    @Test
    void testToString() {
        // Given
        user.setId(1);
        user.setMobile("18852780589");
        user.setPassword("password123");

        // When
        String toString = user.toString();

        // Then
        assertNotNull(toString);
        assertTrue(toString.contains("User"));
        assertTrue(toString.contains("id=1"));
        assertTrue(toString.contains("mobile=18852780589"));
        assertTrue(toString.contains("password=password123"));
    }

    @Test
    void testJsonIgnoreOnPassword() throws JsonProcessingException {
        // Given
        user.setId(1);
        user.setMobile("18852780589");
        user.setPassword("password123");

        // When
        String json = objectMapper.writeValueAsString(user);

        // Then
        assertFalse(json.contains("password"));
        assertFalse(json.contains("password123"));
        assertTrue(json.contains("mobile"));
        assertTrue(json.contains("18852780589"));
        assertTrue(json.contains("id"));
    }

    @Test
    void testJsonDeserialization() throws JsonProcessingException {
        // Given
        String json = "{\"id\":1,\"mobile\":\"18852780589\"}";

        // When
        User deserializedUser = objectMapper.readValue(json, User.class);

        // Then
        assertEquals(1, deserializedUser.getId());
        assertEquals("18852780589", deserializedUser.getMobile());
        assertNull(deserializedUser.getPassword());
    }

    @Test
    void testJsonSerializationWithoutPassword() throws JsonProcessingException {
        // Given
        user.setId(123);
        user.setMobile("13800138000");
        user.setPassword("secretpassword");

        // When
        String json = objectMapper.writeValueAsString(user);
        User deserializedUser = objectMapper.readValue(json, User.class);

        // Then
        assertEquals(user.getId(), deserializedUser.getId());
        assertEquals(user.getMobile(), deserializedUser.getMobile());
        assertNull(deserializedUser.getPassword()); // Password should not be serialized
    }

    @Test
    void testNullValues() {
        // Given
        user.setId(0);
        user.setMobile(null);
        user.setPassword(null);

        // Then
        assertEquals(0, user.getId());
        assertNull(user.getMobile());
        assertNull(user.getPassword());
    }

    @Test
    void testEmptyStringValues() {
        // Given
        user.setId(1);
        user.setMobile("");
        user.setPassword("");

        // Then
        assertEquals(1, user.getId());
        assertEquals("", user.getMobile());
        assertEquals("", user.getPassword());
    }

    @Test
    void testLongMobileNumber() {
        // Given
        String longMobile = "188527805891234567890";
        user.setMobile(longMobile);

        // Then
        assertEquals(longMobile, user.getMobile());
    }

    @Test
    void testSpecialCharactersInPassword() {
        // Given
        String specialPassword = "p@ssw0rd!#$%^&*()";
        user.setPassword(specialPassword);

        // Then
        assertEquals(specialPassword, user.getPassword());
    }

    @Test
    void testNegativeId() {
        // Given
        user.setId(-1);

        // Then
        assertEquals(-1, user.getId());
    }

    @Test
    void testMaxIntId() {
        // Given
        user.setId(Integer.MAX_VALUE);

        // Then
        assertEquals(Integer.MAX_VALUE, user.getId());
    }
}
