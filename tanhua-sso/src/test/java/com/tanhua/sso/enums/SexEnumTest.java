package com.tanhua.sso.enums;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class SexEnumTest {

    @Test
    void testEnumValues() {
        // Test all enum values exist
        SexEnum[] values = SexEnum.values();
        assertEquals(3, values.length);
        
        // Test specific values
        assertEquals(SexEnum.MAN, SexEnum.valueOf("MAN"));
        assertEquals(SexEnum.WOMAN, SexEnum.valueOf("WOMAN"));
        assertEquals(SexEnum.UNKNOWN, SexEnum.valueOf("UNKNOWN"));
    }

    @Test
    void testGetValue() {
        // Test getValue method for each enum
        assertEquals(Integer.valueOf(1), SexEnum.MAN.getValue());
        assertEquals(Integer.valueOf(2), SexEnum.WOMAN.getValue());
        assertEquals(Integer.valueOf(3), SexEnum.UNKNOWN.getValue());
    }

    @Test
    void testToString() {
        // Test toString method for each enum
        assertEquals("男", SexEnum.MAN.toString());
        assertEquals("女", SexEnum.WOMAN.toString());
        assertEquals("未知", SexEnum.UNKNOWN.toString());
    }

    @Test
    void testEnumName() {
        // Test enum name() method
        assertEquals("MAN", SexEnum.MAN.name());
        assertEquals("WOMAN", SexEnum.WOMAN.name());
        assertEquals("UNKNOWN", SexEnum.UNKNOWN.name());
    }

    @Test
    void testEnumOrdinal() {
        // Test enum ordinal() method
        assertEquals(0, SexEnum.MAN.ordinal());
        assertEquals(1, SexEnum.WOMAN.ordinal());
        assertEquals(2, SexEnum.UNKNOWN.ordinal());
    }

    @Test
    void testEnumEquality() {
        // Test enum equality
        assertEquals(SexEnum.MAN, SexEnum.MAN);
        assertEquals(SexEnum.WOMAN, SexEnum.WOMAN);
        assertEquals(SexEnum.UNKNOWN, SexEnum.UNKNOWN);
        
        assertNotEquals(SexEnum.MAN, SexEnum.WOMAN);
        assertNotEquals(SexEnum.MAN, SexEnum.UNKNOWN);
        assertNotEquals(SexEnum.WOMAN, SexEnum.UNKNOWN);
    }

    @Test
    void testEnumHashCode() {
        // Test enum hashCode consistency
        assertEquals(SexEnum.MAN.hashCode(), SexEnum.MAN.hashCode());
        assertEquals(SexEnum.WOMAN.hashCode(), SexEnum.WOMAN.hashCode());
        assertEquals(SexEnum.UNKNOWN.hashCode(), SexEnum.UNKNOWN.hashCode());
    }

    @Test
    void testEnumCompareTo() {
        // Test enum compareTo method (based on ordinal)
        assertTrue(SexEnum.MAN.compareTo(SexEnum.WOMAN) < 0);
        assertTrue(SexEnum.WOMAN.compareTo(SexEnum.UNKNOWN) < 0);
        assertTrue(SexEnum.UNKNOWN.compareTo(SexEnum.MAN) > 0);
        assertEquals(0, SexEnum.MAN.compareTo(SexEnum.MAN));
    }

    @Test
    void testValueOf() {
        // Test valueOf method
        assertEquals(SexEnum.MAN, SexEnum.valueOf("MAN"));
        assertEquals(SexEnum.WOMAN, SexEnum.valueOf("WOMAN"));
        assertEquals(SexEnum.UNKNOWN, SexEnum.valueOf("UNKNOWN"));
    }

    @Test
    void testValueOfInvalidValue() {
        // Test valueOf with invalid value
        assertThrows(IllegalArgumentException.class, () -> {
            SexEnum.valueOf("INVALID");
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            SexEnum.valueOf("男");
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            SexEnum.valueOf("1");
        });
    }

    @Test
    void testValueOfNullValue() {
        // Test valueOf with null value
        assertThrows(NullPointerException.class, () -> {
            SexEnum.valueOf(null);
        });
    }

    @Test
    void testEnumInSwitch() {
        // Test enum usage in switch statement
        for (SexEnum sex : SexEnum.values()) {
            String result = switch (sex) {
                case MAN -> "男性";
                case WOMAN -> "女性";
                case UNKNOWN -> "未知性别";
            };
            
            switch (sex) {
                case MAN:
                    assertEquals("男性", result);
                    break;
                case WOMAN:
                    assertEquals("女性", result);
                    break;
                case UNKNOWN:
                    assertEquals("未知性别", result);
                    break;
            }
        }
    }

    @Test
    void testIEnumInterface() {
        // Test that SexEnum implements IEnum<Integer>
        assertTrue(SexEnum.MAN instanceof com.baomidou.mybatisplus.core.enums.IEnum);
        assertTrue(SexEnum.WOMAN instanceof com.baomidou.mybatisplus.core.enums.IEnum);
        assertTrue(SexEnum.UNKNOWN instanceof com.baomidou.mybatisplus.core.enums.IEnum);
    }

    @Test
    void testEnumConstantValues() {
        // Test that enum constants have correct internal values
        SexEnum man = SexEnum.MAN;
        SexEnum woman = SexEnum.WOMAN;
        SexEnum unknown = SexEnum.UNKNOWN;
        
        // Verify the internal value field through getValue()
        assertEquals(1, man.getValue().intValue());
        assertEquals(2, woman.getValue().intValue());
        assertEquals(3, unknown.getValue().intValue());
        
        // Verify the description through toString()
        assertEquals("男", man.toString());
        assertEquals("女", woman.toString());
        assertEquals("未知", unknown.toString());
    }

    @Test
    void testEnumSerialization() {
        // Test enum serialization behavior
        String manName = SexEnum.MAN.name();
        String womanName = SexEnum.WOMAN.name();
        String unknownName = SexEnum.UNKNOWN.name();
        
        assertEquals("MAN", manName);
        assertEquals("WOMAN", womanName);
        assertEquals("UNKNOWN", unknownName);
        
        // Test that we can recreate enum from name
        assertEquals(SexEnum.MAN, SexEnum.valueOf(manName));
        assertEquals(SexEnum.WOMAN, SexEnum.valueOf(womanName));
        assertEquals(SexEnum.UNKNOWN, SexEnum.valueOf(unknownName));
    }

    @Test
    void testEnumCollectionBehavior() {
        // Test enum in collections
        java.util.Set<SexEnum> sexSet = java.util.EnumSet.allOf(SexEnum.class);
        assertEquals(3, sexSet.size());
        assertTrue(sexSet.contains(SexEnum.MAN));
        assertTrue(sexSet.contains(SexEnum.WOMAN));
        assertTrue(sexSet.contains(SexEnum.UNKNOWN));
    }

    @Test
    void testEnumMapBehavior() {
        // Test enum as map key
        java.util.Map<SexEnum, String> sexMap = new java.util.EnumMap<>(SexEnum.class);
        sexMap.put(SexEnum.MAN, "Male");
        sexMap.put(SexEnum.WOMAN, "Female");
        sexMap.put(SexEnum.UNKNOWN, "Unknown");
        
        assertEquals("Male", sexMap.get(SexEnum.MAN));
        assertEquals("Female", sexMap.get(SexEnum.WOMAN));
        assertEquals("Unknown", sexMap.get(SexEnum.UNKNOWN));
        assertEquals(3, sexMap.size());
    }
}
