package com.tanhua.sso.vo;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class ErrorResultTest {

    @Test
    void testBuilderPattern() {
        // When
        ErrorResult errorResult = ErrorResult.builder()
                .errCode("000001")
                .errMessage("测试错误信息")
                .build();

        // Then
        assertEquals("000001", errorResult.getErrCode());
        assertEquals("测试错误信息", errorResult.getErrMessage());
    }

    @Test
    void testBuilderWithNullValues() {
        // When
        ErrorResult errorResult = ErrorResult.builder()
                .errCode(null)
                .errMessage(null)
                .build();

        // Then
        assertNull(errorResult.getErrCode());
        assertNull(errorResult.getErrMessage());
    }

    @Test
    void testBuilderWithEmptyValues() {
        // When
        ErrorResult errorResult = ErrorResult.builder()
                .errCode("")
                .errMessage("")
                .build();

        // Then
        assertEquals("", errorResult.getErrCode());
        assertEquals("", errorResult.getErrMessage());
    }

    @Test
    void testBuilderPartialValues() {
        // Test with only errCode
        ErrorResult errorResult1 = ErrorResult.builder()
                .errCode("000001")
                .build();

        assertEquals("000001", errorResult1.getErrCode());
        assertNull(errorResult1.getErrMessage());

        // Test with only errMessage
        ErrorResult errorResult2 = ErrorResult.builder()
                .errMessage("错误信息")
                .build();

        assertNull(errorResult2.getErrCode());
        assertEquals("错误信息", errorResult2.getErrMessage());
    }

    @Test
    void testSettersAndGetters() {
        // Given
        ErrorResult errorResult = ErrorResult.builder().build();

        // When
        errorResult.setErrCode("000002");
        errorResult.setErrMessage("设置的错误信息");

        // Then
        assertEquals("000002", errorResult.getErrCode());
        assertEquals("设置的错误信息", errorResult.getErrMessage());
    }

    @Test
    void testEqualsAndHashCode() {
        // Given
        ErrorResult errorResult1 = ErrorResult.builder()
                .errCode("000001")
                .errMessage("错误信息")
                .build();

        ErrorResult errorResult2 = ErrorResult.builder()
                .errCode("000001")
                .errMessage("错误信息")
                .build();

        ErrorResult errorResult3 = ErrorResult.builder()
                .errCode("000002")
                .errMessage("不同的错误信息")
                .build();

        // Then
        assertEquals(errorResult1, errorResult2);
        assertEquals(errorResult1.hashCode(), errorResult2.hashCode());
        assertNotEquals(errorResult1, errorResult3);
        assertNotEquals(errorResult1.hashCode(), errorResult3.hashCode());
    }

    @Test
    void testToString() {
        // Given
        ErrorResult errorResult = ErrorResult.builder()
                .errCode("000001")
                .errMessage("测试错误")
                .build();

        // When
        String toString = errorResult.toString();

        // Then
        assertNotNull(toString);
        assertTrue(toString.contains("ErrorResult"));
        assertTrue(toString.contains("errCode=000001"));
        assertTrue(toString.contains("errMessage=测试错误"));
    }

    @Test
    void testBuilderChaining() {
        // When
        ErrorResult errorResult = ErrorResult.builder()
                .errCode("000001")
                .errMessage("第一个错误")
                .errCode("000002")  // Override previous errCode
                .errMessage("第二个错误")  // Override previous errMessage
                .build();

        // Then
        assertEquals("000002", errorResult.getErrCode());
        assertEquals("第二个错误", errorResult.getErrMessage());
    }

    @Test
    void testCommonErrorCodes() {
        // Test common error scenarios
        ErrorResult success = ErrorResult.builder()
                .errCode("000000")
                .errMessage("成功")
                .build();

        ErrorResult validationError = ErrorResult.builder()
                .errCode("000001")
                .errMessage("参数验证失败")
                .build();

        ErrorResult systemError = ErrorResult.builder()
                .errCode("999999")
                .errMessage("系统内部错误")
                .build();

        assertEquals("000000", success.getErrCode());
        assertEquals("成功", success.getErrMessage());

        assertEquals("000001", validationError.getErrCode());
        assertEquals("参数验证失败", validationError.getErrMessage());

        assertEquals("999999", systemError.getErrCode());
        assertEquals("系统内部错误", systemError.getErrMessage());
    }

    @Test
    void testLongErrorMessages() {
        // Given
        String longMessage = "这是一个非常长的错误信息，用来测试ErrorResult是否能够正确处理长文本内容，" +
                "包含中文字符和各种标点符号！@#$%^&*()_+-=[]{}|;':\",./<>?";

        // When
        ErrorResult errorResult = ErrorResult.builder()
                .errCode("000001")
                .errMessage(longMessage)
                .build();

        // Then
        assertEquals("000001", errorResult.getErrCode());
        assertEquals(longMessage, errorResult.getErrMessage());
    }

    @Test
    void testSpecialCharactersInErrorCode() {
        // Given
        String specialCode = "ERR-001_TEST@2023";

        // When
        ErrorResult errorResult = ErrorResult.builder()
                .errCode(specialCode)
                .errMessage("特殊字符错误码测试")
                .build();

        // Then
        assertEquals(specialCode, errorResult.getErrCode());
        assertEquals("特殊字符错误码测试", errorResult.getErrMessage());
    }

    @Test
    void testNumericErrorCodes() {
        // Test various numeric error codes
        String[] errorCodes = {"0", "1", "100", "404", "500", "999999"};
        
        for (String code : errorCodes) {
            ErrorResult errorResult = ErrorResult.builder()
                    .errCode(code)
                    .errMessage("错误码: " + code)
                    .build();
            
            assertEquals(code, errorResult.getErrCode());
            assertEquals("错误码: " + code, errorResult.getErrMessage());
        }
    }

    @Test
    void testBuilderReuse() {
        // Given
        ErrorResult.ErrorResultBuilder builder = ErrorResult.builder()
                .errCode("000001")
                .errMessage("基础错误");

        // When
        ErrorResult errorResult1 = builder.build();
        ErrorResult errorResult2 = builder.errMessage("修改后的错误").build();

        // Then
        assertEquals("000001", errorResult1.getErrCode());
        assertEquals("基础错误", errorResult1.getErrMessage());

        assertEquals("000001", errorResult2.getErrCode());
        assertEquals("修改后的错误", errorResult2.getErrMessage());
    }

    @Test
    void testEqualsWithNullValues() {
        // Given
        ErrorResult errorResult1 = ErrorResult.builder().build();
        ErrorResult errorResult2 = ErrorResult.builder().build();
        ErrorResult errorResult3 = ErrorResult.builder()
                .errCode("000001")
                .build();

        // Then
        assertEquals(errorResult1, errorResult2);
        assertNotEquals(errorResult1, errorResult3);
        assertNotEquals(errorResult2, errorResult3);
    }

    @Test
    void testHashCodeConsistency() {
        // Given
        ErrorResult errorResult = ErrorResult.builder()
                .errCode("000001")
                .errMessage("测试")
                .build();

        // When
        int hashCode1 = errorResult.hashCode();
        int hashCode2 = errorResult.hashCode();

        // Then
        assertEquals(hashCode1, hashCode2);
    }
}
