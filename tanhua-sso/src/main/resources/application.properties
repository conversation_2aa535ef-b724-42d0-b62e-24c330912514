spring.application.name=org.tanhua-sso
server.port=8080

spring.datasource.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.url=****************************************************************************************
spring.datasource.username=root
spring.datasource.password=201041xu

mybatis-plus.type-enums-package=com.tanhua.sso.enums
mybatis-plus.global-config.db-config.table-prefix=tb_
mybatis-plus.global-config.db-config.id-type=auto

spring.redis.jedis.pool.max-wait=5000ms
spring.redis.jedis.pool.max-idle=100
spring.redis.jedis.pool.min-idle=10
spring.redis.timeout=10s
spring.redis.cluster.nodes=*************:6379, *************:6380, *************:6381
spring.redis.cluster.max-redirects=5

rocketmq.nameServer = *************:9876
rocketmq.producer.group = tanhua

jwt.secret = 76bd425b6f29f7fcc2e0bfc286043df1
