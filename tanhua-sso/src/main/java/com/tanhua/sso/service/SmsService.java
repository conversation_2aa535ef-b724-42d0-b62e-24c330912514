package com.tanhua.sso.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

@Service
public class SmsService {

    private static final Logger logger = LoggerFactory.getLogger(SmsService.class);

    @Autowired
    private RestTemplate restTemplate;

    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    public Map<String, Object> sendCheckCode(String mobile) {
        Map<String, Object> result = new HashMap<>(2);
        try {
            String redisKey = "CHECK_CODE_" + mobile;
            String value = this.redisTemplate.opsForValue().get(redisKey);
            if (StringUtils.isNotEmpty(value)) {
                result.put("code", 1);
                result.put("msg", "上一次验证码未失效");
                return result;
            }
            String code = this.sendSms(mobile);
            if (code == null) {
                result.put("code", 2);
                result.put("msg", "发送验证码失败");
                return result;
            }

            result.put("code", 3);
            result.put("msg", "ok");

            this.redisTemplate.opsForValue().set(redisKey, code, Duration.ofMinutes(2));
        } catch (Exception e) {
            logger.error("验证码出错{}", mobile, e);
            result.put("code", 4);
            result.put("msg", "发送验证码异常");
            return result;
        }
        return result;
    }

    // 不发送了，直接本地随机生成一个
    public String sendSms(String mobile) {
//        String url = "https://open.ucpaas.com/ol/sms/sendsms";
        Map<String, Object> params = new HashMap<>();
        params.put("mobile", mobile);
        int checkCode = RandomUtils.nextInt(100000, 999999);
        System.out.println(checkCode);
        // 生成6位数验证
        params.put("param", checkCode);

        return String.valueOf(params.get("param"));
    }
}
