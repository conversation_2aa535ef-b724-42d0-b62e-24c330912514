package com.tanhua.sso.controller;

import com.baomidou.mybatisplus.extension.api.R;
import com.tanhua.sso.service.SmsService;
import com.tanhua.sso.vo.ErrorResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("user")
public class SmsController {

    @Autowired
    private SmsService smsService;

    @PostMapping("login")
    public ResponseEntity<Object> sendCheckCode(@RequestBody Map<String, Object> para) {
        ErrorResult.ErrorResultBuilder builder = ErrorResult.builder().errCode("000000").errMessage("短信发送失败");
        String phone = String.valueOf(para.get("phone"));
        Map<String, Object> map = this.smsService.sendCheckCode(phone);
        int code = ((Integer) (map.get("code"))).intValue();
        if (code == 3) {
            return ResponseEntity.ok(null);
        } else if (code == 1) {
            String msg = map.get("msg").toString();
            builder.errCode("000001").errMessage(msg);
        }
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(builder.build());
    }
}
